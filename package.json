{"name": "vue-template", "version": "0.0.0", "private": true, "type": "module", "packageManager": "pnpm@9.0.6", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "prettier": "prettier --write .", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx,.md, --max-warnings 0 --cache", "lint:fix": "pnpm run lint --fix", "test": "vitest --run --coverage --isolate", "style": "stylelint \"src/**/*.(vue|scss|css)\" --fix"}, "dependencies": {"element-plus": "^2.8.0", "leaflet": "^1.9.4", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "vue": "^3.4.25", "vue-router": "4.3.0"}, "devDependencies": {"@types/leaflet": "^1.9.12", "@types/node": "^20.12.7", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "@unocss/preset-rem-to-px": "^0.60.3", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-v8": "^1.5.1", "@vue/test-utils": "^2.4.5", "@vue/tsconfig": "^0.5.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsonc": "^2.11.1", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unicorn": "^52.0.0", "eslint-plugin-vue": "^9.19.2", "happy-dom": "15.10.2", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.75.0", "stylelint": "^16.5.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "typescript": "^5.4.5", "unocss": "^0.59.4", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "5.4.6", "vite-plugin-restart": "^0.4.0", "vite-plugin-vue-devtools": "^7.2.1", "vitest": "^1.5.1", "vue-tsc": "^2.0.14"}}