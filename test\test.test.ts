/*
 * @Author: thunderchen
 * @Date: 2024-03-28 15:19:37
 * @LastEditTime: 2024-03-28 15:25:48
 * @email: <EMAIL>
 * @Description: example
 * ---------------------------------------------
 * import {mount} from "@vue/test-utils"
 * import {test,expect,describe} from "vitest"
 * import testVue from ".vue"
 *   describe('testVue', () => {
 *    test('it should output text', () => {
 *        const wrapper = mount(testVue, {
 *            props: {
 *                number: 0,
 *            }
 *          })
 *        // 断言输出
 *        expect(wrapper.html()).toContain('0')
 *      })
 *    })
 */
