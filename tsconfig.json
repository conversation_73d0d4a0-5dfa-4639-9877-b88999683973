{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": false,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["ESNext", "DOM"],
    "skipLibCheck": true,
    "baseUrl": ".",
    /*允许引入js文件*/
    "allowJs": true,
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["types/*"],
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"],
  "exclude": ["node_modules", "dist", "**/*.js", "**/*.svg"],
  "references": [{ "path": "./tsconfig.node.json" }],
  "extends": "@vue/tsconfig/tsconfig.dom.json"
}
